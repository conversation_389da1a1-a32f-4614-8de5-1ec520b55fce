version: '3.8'

services:
  db:
    image: postgres:17.2-alpine
    container_name: character-network-db-dev
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=characternetwork
    ports:
      - "5432:5432"
    volumes:
      - ./db-dev:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5