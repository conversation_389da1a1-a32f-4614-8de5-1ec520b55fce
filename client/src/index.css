@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer components {
  /* Waterfall Grid Layout */
  .waterfall-grid {
    @apply grid gap-4;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }

  @media (min-width: 640px) {
    .waterfall-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
  }

  @media (min-width: 768px) {
    .waterfall-grid {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
  }

  @media (min-width: 1024px) {
    .waterfall-grid {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
  }

  /* 隐藏滚动条但保留功能 */
  .hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* 关系图谱容器样式 */
  .graph-container {
    @apply overflow-hidden;
  }

  /* 节点样式 */
  .graph-node {
    @apply cursor-pointer transition-opacity duration-200;
  }

  .graph-node circle {
    @apply transition-all duration-200;
  }

  .graph-node:hover circle {
    @apply stroke-white stroke-2;
  }

  .node-label {
    @apply fill-white font-semibold select-none;
  }

  .node-name {
    @apply fill-gray-100 font-medium drop-shadow-md text-xs select-none;
  }

  /* 连接线样式 */
  .graph-link line {
    @apply transition-opacity duration-200;
  }

  .link-label {
    @apply fill-gray-200 text-xs font-medium select-none drop-shadow-sm;
  }

  /* 节点信息面板 */
  .node-info-panel {
    @apply bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-3 max-w-xs;
  }

  /* 工具栏样式 */
  .graph-toolbar {
    @apply bg-gray-100 rounded-lg p-2 flex flex-wrap gap-2 items-center mb-4;
  }

  /* 移动端动画效果 */
  .fade-in {
    @apply transition-opacity duration-300 ease-in-out;
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .slide-up {
    @apply transition-transform duration-300 ease-in-out;
    animation: slideUp 0.3s ease-in-out;
  }
  
  .scale-in {
    @apply transition-transform duration-300 ease-in-out;
    animation: scaleIn 0.3s ease-in-out;
  }
  
  .bounce-in {
    animation: bounceIn 0.5s ease-in-out;
  }
  
  @keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }
  
  @keyframes slideUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes scaleIn {
    0% { transform: scale(0.95); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }
  
  @keyframes bounceIn {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.05); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
  }
  
  /* 移动菜单动画 */
  @keyframes slideInFromLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
  }
  
  @keyframes slideOutToLeft {
    from { transform: translateX(0); }
    to { transform: translateX(-100%); }
  }
  
  .mobile-menu-enter {
    animation: slideInFromLeft 0.3s forwards;
  }
  
  .mobile-menu-exit {
    animation: slideOutToLeft 0.3s forwards;
  }

  /* 移动端优化 */
  .touch-callout-none {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  /* 适配移动端的底部栏 */
  .mobile-tabbar {
    @apply fixed bottom-0 left-0 right-0 flex items-center justify-around bg-background border-t border-border py-2 px-1 safe-bottom z-50 shadow-md;
    position: fixed !important; /* 确保固定在底部 */
    height: 60px; /* 固定高度 */
  }

  .mobile-tabbar-item {
    @apply flex flex-col items-center justify-center flex-1 py-1 touch-callout-none transition-colors duration-200;
  }

  .mobile-tabbar-item-active {
    @apply text-primary font-medium;
  }

  .mobile-tabbar-item-active svg {
    @apply text-primary;
    filter: drop-shadow(0 0 2px rgba(var(--primary-rgb), 0.3));
    transform: scale(1.1);
  }

  .mobile-tabbar-label {
    @apply text-xs mt-1 transition-all;
  }
  
  .mobile-tabbar-item-active .mobile-tabbar-label {
    @apply font-medium;
    transform: translateY(-1px);
  }

  /* 移动端下拉刷新动画 */
  .pull-to-refresh {
    @apply w-full flex items-center justify-center py-3;
  }
  
  .pull-to-refresh-icon {
    @apply animate-spin h-5 w-5 text-primary;
  }

  /* 列表组件优化 */
  .mobile-list {
    @apply divide-y divide-border overflow-auto;
  }

  .mobile-list-item {
    @apply py-3 px-4 flex items-center gap-3 active:bg-accent/50 transition-colors;
  }

  /* 移动端点击反馈 */
  .tap-highlight {
    @apply active:opacity-70 transition-opacity duration-100;
  }

  /* 移动端手势滑动区域 */
  .swipe-area {
    @apply touch-pan-y overscroll-y-contain;
  }

  /* 自适应内容区域 */
  .mobile-content-area {
    @apply max-w-md mx-auto px-4 pb-20 pt-4;
  }
}