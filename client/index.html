<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="管理和可视化小说中的人物关系网络的应用" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="小说人物关系">
  <link rel="icon" type="image/svg+xml" href="/icons/icon-universal.svg" />
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
  <link rel="mask-icon" href="/icons/icon-universal.svg" color="#3b82f6">
  <link rel="manifest" href="/manifest.json" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <title>小说人物关系管理器</title>
  <style>
    .waterfall-grid {
      column-count: 1;
      column-gap: 1rem;
    }
    @media (min-width: 640px) {
      .waterfall-grid {
        column-count: 2;
      }
    }
    @media (min-width: 768px) {
      .waterfall-grid {
        column-count: 3;
      }
    }
    @media (min-width: 1024px) {
      .waterfall-grid {
        column-count: 4;
      }
    }
    .waterfall-item {
      break-inside: avoid;
      margin-bottom: 1rem;
    }
    .network-graph {
      position: relative;
      width: 100%;
      height: 400px;
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      overflow: hidden;
      background-color: #f8fafc;
    }
    .node {
      position: absolute;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-size: cover;
      background-position: center;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      cursor: pointer;
      transition: transform 0.2s;
      text-align: center;
      font-size: 0.8rem;
      padding: 5px;
      word-break: break-word;
      overflow-wrap: break-word;
    }
    .node:hover {
      transform: scale(1.1);
    }
    .node-tooltip {
      position: absolute;
      background: white;
      padding: 0.5rem;
      border-radius: 0.25rem;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      z-index: 10;
      width: 150px;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.2s;
    }
    .edge {
      position: absolute;
      height: 2px;
      background-color: #94a3b8;
      transform-origin: left center;
      z-index: 1;
    }
    .edge-label {
      position: absolute;
      background: white;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
      box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      z-index: 2;
      white-space: nowrap;
    }
  </style>
  <script>
    // 禁用React开发时的hooks错误提示
    window.addEventListener('error', function(event) {
      if (event && event.message && event.message.includes('Rendered fewer hooks than expected')) {
        event.preventDefault();
        event.stopPropagation();
        console.warn('Suppressed React hooks warning:', event.message);
        return true;
      }
    }, true);
  </script>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>
</html>
