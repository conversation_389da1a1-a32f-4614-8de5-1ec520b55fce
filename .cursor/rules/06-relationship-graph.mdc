---
description: 
globs: 
alwaysApply: false
---
# 关系图功能

这个应用的核心功能是管理和可视化小说中人物之间的关系网络。

## 数据模型

关系图的数据模型由以下几个部分组成：

- 角色（Characters）：作为图中的节点
- 关系（Relationships）：作为图中的边，连接两个角色
- 关系类型（RelationshipTypes）：定义关系的种类和颜色

详细的数据模型定义在[shared/schema.ts](mdc:shared/schema.ts)中。

## 关系定义

每个关系都包含以下几个关键字段：

- `sourceId`：关系的起始角色ID
- `targetId`：关系的目标角色ID
- `typeId`：关系类型ID
- `description`：关系描述
- `novelId`：所属小说ID

关系类型包含以下字段：

- `name`：关系类型名称
- `color`：关系类型颜色
- `userId`：创建该类型的用户ID

## API接口

关系相关的API接口定义在[shared/api-types.ts](mdc:shared/api-types.ts)中，包括：

- 获取关系列表
- 获取关系详情
- 创建关系
- 更新关系
- 删除关系

关系类型的API接口包括：

- 获取关系类型列表
- 获取关系类型详情
- 创建关系类型
- 更新关系类型
- 删除关系类型

## 前端实现

前端使用D3.js库实现关系图的可视化，相关实现在客户端代码中。关系图组件允许用户：

- 可视化查看角色关系网络
- 交互式探索关系
- 添加、编辑和删除关系
- 自定义关系类型和颜色
